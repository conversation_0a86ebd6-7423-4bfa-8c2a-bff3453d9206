# -*- coding: utf-8 -*-
# 购物订单模块

from fastapi import APIRouter, Depends, HTTPException, Header
from typing import Dict, Any, Optional
from sqlalchemy.orm import Session

from app.core.deps import get_db
from app.service.wechat_miniapp.wx_user import WeChatUserService
from app.dao.account import account_dao
from app.service.order import order_service
from app.utils.logger import logger
from app.models.order import OrderType

router = APIRouter()


@router.post("/shopping-order/create")
async def create_shopping_order(
        task_info: dict,
        token: Optional[str] = Header(None),
        db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """创建购物订单
    
    Args:
        task_info: 订单信息，包含：
            - order_type: 订单类型，固定为"shopping"
            - items: 商品列表，每个商品包含dish_id、name、price、quantity
            - total_amount: 订单总金额
            - coupon_id: 优惠券ID字符串，格式如"14,17"
            - coupon_discount: 预期优惠金额
        token: 用户token
        db: 数据库会话
    """
    logger.info(f"创建购物订单，接收到的参数: {task_info}")
    
    try:
        # 验证用户token
        user = WeChatUserService.verify_token(db, token)
        if not user:
            logger.warning(f"token无效或已过期: {token[:10] if token else 'None'}...")
            raise HTTPException(
                status_code=401,
                detail={"message": "未登录", "status": 401}
            )
        
        user_id = user.id
        logger.info(f"用户token验证成功，用户ID: {user_id}")
        
        # 提取优惠券参数
        coupon_id_str = task_info.get("coupon_id", "")
        coupon_discount = task_info.get("coupon_discount", 0)
        
        # 解析优惠券ID列表
        coupon_usage_record_ids = []
        if coupon_id_str and coupon_id_str.strip():
            try:
                coupon_usage_record_ids = [int(id.strip()) for id in coupon_id_str.split(",") if id.strip()]
                logger.info(f"解析优惠券ID列表: {coupon_usage_record_ids}")
            except ValueError as e:
                logger.error(f"优惠券ID格式错误: {coupon_id_str}, 错误: {str(e)}")
                return {
                    "message": "优惠券ID格式错误",
                    "status": 400
                }
        
        logger.info(f"优惠券参数 - coupon_usage_record_ids: {coupon_usage_record_ids}, coupon_discount: {coupon_discount}")
        
        # 验证必填参数
        required_fields = ['order_type', 'items', 'total_amount']
        
        for field in required_fields:
            if field not in task_info or task_info[field] is None:
                logger.error(f"参数错误，缺少必填字段: {field}")
                return {
                    "message": f"缺少必填参数: {field}",
                    "status": 400
                }
        
        # 验证订单类型
        if task_info['order_type'] != 'shopping':
            logger.error(f"订单类型错误: {task_info['order_type']}")
            return {
                "message": "订单类型必须为shopping",
                "status": 400
            }
        
        # 验证商品列表
        items = task_info['items']
        if not items or not isinstance(items, list):
            logger.error("商品列表为空或格式错误")
            return {
                "message": "商品列表不能为空",
                "status": 400
            }
        
        # 构建产品列表（简化版本，不包含预订信息）
        products = []
        for item in items:
            # 验证商品必填字段
            item_required_fields = ['dish_id', 'quantity']
            for field in item_required_fields:
                if field not in item or item[field] is None:
                    logger.error(f"商品参数错误，缺少必填字段: {field}")
                    return {
                        "message": f"商品缺少必填参数: {field}",
                        "status": 400
                    }
            
            # 验证数量为正整数
            try:
                quantity = int(item['quantity'])
                if quantity <= 0:
                    raise ValueError("数量必须为正整数")
            except (ValueError, TypeError):
                logger.error(f"商品数量格式错误: {item.get('quantity')}")
                return {
                    "message": "商品数量必须为正整数",
                    "status": 400
                }
            
            products.append({
                "product_id": int(item["dish_id"]),
                "quantity": quantity
            })
        
        logger.info(f"创建商品列表: {products}")
        
        # 创建订单，传递优惠券参数（不传递rule_data）
        order = order_service.create_order(
            db, 
            user_id, 
            products,
            rule_data=None,  # 购物订单不需要规则数据
            coupon_usage_record_ids=coupon_usage_record_ids,
            order_type=OrderType.DIRECT_SALE
        )
        
        order_id = order.id
        order_no = order.order_no
        order_payable_amount = order.payable_amount
        
        logger.info(f"创建订单: {order}")
        logger.info(f"创建的订单号: {order_no}")
        logger.info(f"创建的订单金额: {order_payable_amount}")
        logger.info(f"订单优惠金额: {order.total_discount_amount}")
        
        # 验证优惠金额
        if coupon_usage_record_ids and coupon_discount > 0:
            actual_discount = order.total_discount_amount or 0.0
            if abs(actual_discount - coupon_discount) > 0.01:  # 允许0.01的误差
                logger.error(f"优惠金额不匹配 - 预期: {coupon_discount}, 实际: {actual_discount}")
                db.rollback()
                return {
                    "message": f"优惠金额验证失败，预期优惠{coupon_discount}元，实际优惠{actual_discount}元",
                    "status": 400
                }
            logger.info(f"优惠金额验证通过 - 预期: {coupon_discount}, 实际: {actual_discount}")
        
        # 获取用户余额
        user_balance = account_dao.get_user_balance(db, user_id)
        logger.info(f"用户余额: {user_balance}")
        
        return {
            'order_no': order_no,
            'order_id': order_id,
            'payable_amount': order_payable_amount,
            'user_balance': user_balance
        }
        
    except Exception as e:
        logger.error(f"创建购物订单失败: {str(e)}", exc_info=True)
        db.rollback()
        return {
            "message": f"创建订单失败: {str(e)}",
            "status": 500
        }
