# CouponService.coupon_pricing 方法调用关联图

## 主方法
```
CouponService.coupon_pricing(session, user_id, order_items, coupon_usage_record_ids)
```

## 方法调用层次结构

### 第一层：直接调用的方法

```
coupon_pricing()
├── 1. validate_coupons_for_pricing()
├── 2. calculate_coupon_discounts()
└── 3. _get_user_coupon_lists()
```

### 第二层：validate_coupons_for_pricing() 调用的方法

```
validate_coupons_for_pricing()
└── _validate_single_coupon()
    ├── _check_coupon_status()
    ├── _check_coupon_validity_period()
    ├── _check_mutual_exclusive_rules()
    ├── _check_order_amount_condition_for_pricing()
    ├── _check_product_condition_for_pricing()
    └── _check_usage_cycle_limit()
        ├── _get_cycle_start_time()
        └── _get_cycle_name()
```

### 第二层：calculate_coupon_discounts() 调用的方法

```
calculate_coupon_discounts()
├── _convert_order_items_to_dict()
├── _apply_order_level_coupon()
├── _distribute_order_discount()
├── _apply_product_level_coupon()
└── _calculate_product_discount()
```

### 第二层：_get_user_coupon_lists() 调用的方法

```
_get_user_coupon_lists()
├── _get_cycle_start_time()
├── _get_cycle_name()
└── 内部检查逻辑：
    ├── 有效期检查
    ├── 互斥规则检查
    ├── 使用周期限制检查
    ├── 订单金额条件检查
    ├── 产品组合条件检查
    └── 产品类型条件检查 (condition_product_types) ✨新增
```

## 完整调用链路图

```
coupon_pricing()
│
├── validate_coupons_for_pricing()
│   └── _validate_single_coupon()
│       ├── _check_coupon_status()
│       ├── _check_coupon_validity_period()
│       ├── _check_mutual_exclusive_rules()
│       ├── _check_order_amount_condition_for_pricing()
│       ├── _check_product_condition_for_pricing()
│       └── _check_usage_cycle_limit()
│           ├── _get_cycle_start_time()
│           └── _get_cycle_name()
│
├── calculate_coupon_discounts()
│   ├── _convert_order_items_to_dict()
│   ├── 根据优惠券类型选择：
│   │   ├── _apply_order_level_coupon()
│   │   │   └── _distribute_order_discount()
│   │   └── _apply_product_level_coupon()
│   │       └── _calculate_product_discount()
│   └── 构建优惠结果
│
└── _get_user_coupon_lists()
    ├── _get_cycle_start_time()
    ├── _get_cycle_name()
    └── 内部检查逻辑（包含condition_product_types检查）
```

## 方法功能说明

### 核心方法
- **coupon_pricing**: 优惠券计价核心方法，协调整个计价流程
- **validate_coupons_for_pricing**: 验证优惠券是否可用于计价
- **calculate_coupon_discounts**: 计算优惠券优惠金额
- **_get_user_coupon_lists**: 获取用户优惠券列表（已使用/可用/不可用）

### 验证相关方法
- **_validate_single_coupon**: 验证单张优惠券
- **_check_coupon_status**: 检查优惠券状态
- **_check_coupon_validity_period**: 检查优惠券有效期
- **_check_mutual_exclusive_rules**: 检查互斥规则
- **_check_order_amount_condition_for_pricing**: 检查订单金额条件
- **_check_product_condition_for_pricing**: 检查产品条件（包含condition_product_types）
- **_check_usage_cycle_limit**: 检查使用周期限制

### 计算相关方法
- **_convert_order_items_to_dict**: 转换订单项为字典格式
- **_apply_order_level_coupon**: 应用订单级别优惠券（支持apply_product_types）
- **_apply_product_level_coupon**: 应用产品级别优惠券（支持apply_product_types）
- **_calculate_product_discount**: 计算单个产品的优惠金额
- **_distribute_order_discount**: 分摊订单级别优惠（支持apply_product_types）

### 辅助方法
- **_get_cycle_start_time**: 获取周期开始时间
- **_get_cycle_name**: 获取周期名称

## 新增功能说明

### condition_product_types 支持
- 在 `_check_product_condition_for_pricing` 中添加了对 `condition_product_types` 的检查
- 在 `_get_user_coupon_lists` 中添加了对 `condition_product_types` 的检查 ✨新增
- 确保订单中包含指定产品类型才能使用优惠券

### apply_product_types 支持
- 在 `_apply_order_level_coupon` 中支持按产品类型限制计算范围
- 在 `_apply_product_level_coupon` 中支持按产品类型筛选适用产品
- 在 `_distribute_order_discount` 中支持按产品类型限制优惠分摊

## 执行流程
1. **验证阶段**: 检查所有优惠券是否符合使用条件
2. **计算阶段**: 按优先级应用优惠券，计算优惠金额
3. **汇总阶段**: 整理计价结果和优惠券列表
4. **返回结果**: 包含订单总金额、应付金额、优惠详情和优惠券分类列表
```
