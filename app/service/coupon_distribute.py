from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
from sqlalchemy import and_, func

from app.dao.coupon import (
    coupon_dao, coupon_usage_record_dao, coupon_batch_dao, 
    coupon_distribution_rule_dao
)
from app.schemas.coupon import CouponUsageRecordCreate
from app.models.coupon import (
    CouponUsageStatus, CouponUsageCycle, CouponScope, CouponType,
    DistributionChannel, DistributionRuleType, CouponUsageRecord
)
from app.models.enum import Status
from app.models.order import OrderStatus, PaymentStatus
from app.models.product import ProductType, ObjectType
from app.events.models import UserEvent, UserEventAction
from app.service.user_behavior_func import get_user_behavior_function
from app.utils.logger import logger


class CouponDistributeService:
    """优惠券分发服务类
    
    提供优惠券分发相关的业务逻辑处理，包括手动发放、按规则发放、基于事件发放等操作
    """

    @staticmethod
    def distribute_coupons_manually(session: Session, user_ids: List[int], coupon_id: int, coupon_batch_id: int) -> Dict[str, Any]:
        """指定发放优惠券

        根据user_id,coupon_id,coupon_batch_id作为传入参数，支持批量操作，
        即对多个user_id发放同一批次的优惠券

        Args:
            session: 数据库会话
            user_ids: 用户ID列表
            coupon_id: 优惠券ID
            coupon_batch_id: 优惠券批次ID

        Returns:
            Dict[str, Any]: 发放结果，包含成功数量、失败数量等信息
        """
        try:
            # 1. 检查优惠券批次是否存在且有效
            coupon_batch = coupon_batch_dao.get(session, coupon_batch_id)
            if not coupon_batch:
                return {
                    "success": False,
                    "message": "优惠券批次不存在",
                    "distributed_count": 0,
                    "failed_count": len(user_ids),
                    "failed_users": user_ids
                }

            # 2. 检查优惠券是否存在且有效
            coupon = coupon_dao.get(session, coupon_id)
            if not coupon:
                return {
                    "success": False,
                    "message": "优惠券不存在",
                    "distributed_count": 0,
                    "failed_count": len(user_ids),
                    "failed_users": user_ids
                }

            # 3. 检查批次数量是否满足本次发放要求
            required_quantity = len(user_ids)
            if coupon_batch.quantity < required_quantity:
                return {
                    "success": False,
                    "message": f"批次剩余数量不足，需要{required_quantity}张，剩余{coupon_batch.quantity}张",
                    "distributed_count": 0,
                    "failed_count": len(user_ids),
                    "failed_users": user_ids
                }

            # 4. 批量创建优惠券使用记录
            distributed_count = 0
            failed_users = []

            for user_id in user_ids:
                try:
                    # 创建优惠券使用记录
                    usage_record_create = CouponUsageRecordCreate(
                        coupon_id=coupon_id,
                        coupon_batch_id=coupon_batch_id,
                        user_id=user_id,
                        discount_amount=0.0,  # 初始为0，使用时计算
                        distribution_channel=DistributionChannel.MANUAL,
                        status=CouponUsageStatus.VALID
                    )

                    coupon_usage_record_dao.create(session, usage_record_create)
                    distributed_count += 1

                except Exception as e:
                    logger.error(f"为用户{user_id}发放优惠券失败: {str(e)}")
                    failed_users.append(user_id)

            # 5. 更新优惠券批次数量
            if distributed_count > 0:
                new_quantity = coupon_batch.quantity - distributed_count
                coupon_batch_dao.update(session, coupon_batch_id, quantity=new_quantity)

            # 6. 返回发放结果
            return {
                "success": distributed_count > 0,
                "message": f"成功发放{distributed_count}张优惠券" if distributed_count > 0 else "发放失败",
                "distributed_count": distributed_count,
                "failed_count": len(failed_users),
                "failed_users": failed_users,
                "remaining_quantity": coupon_batch.quantity - distributed_count
            }

        except Exception as e:
            logger.error(f"批量发放优惠券失败: {str(e)}")
            return {
                "success": False,
                "message": f"发放失败: {str(e)}",
                "distributed_count": 0,
                "failed_count": len(user_ids),
                "failed_users": user_ids
            }

    @staticmethod
    def distribute_coupons_by_order(session: Session, order) -> Dict[str, Any]:
        """根据订单发放优惠券
        
        检查订单中包含的虚拟产品（优惠券批次），并为用户发放对应的优惠券
        
        Args:
            session: 数据库会话
            order: 订单对象
            
        Returns:
            Dict[str, Any]: 发放结果，包含成功数量、失败数量等信息
        """
        try:
            # 1. 检查订单状态和支付状态
            if order.status != OrderStatus.PAID or order.payment_status != PaymentStatus.PAID:
                return {
                    "success": False,
                    "message": f"订单状态或支付状态不符合要求。订单状态: {order.status.value}, 支付状态: {order.payment_status.value}",
                    "distributed_count": 0,
                    "failed_count": 0,
                    "distributed_coupons": []
                }
            
            # 2. 遍历订单项，查找虚拟产品（优惠券批次）
            distributed_coupons = []
            total_distributed_count = 0
            failed_items = []
            
            for order_item in order.items:
                try:
                    # 检查产品是否为虚拟产品
                    if order_item.product.type != ProductType.VIRTUAL:
                        continue
                    
                    # 检查虚拟产品是否为优惠券批次类型
                    virtual_product = order_item.product
                    if virtual_product.object_type != ObjectType.COUPON_BATCH:
                        continue
                    
                    # 获取优惠券批次
                    coupon_batch_id = virtual_product.object_id
                    coupon_batch = coupon_batch_dao.get(session, coupon_batch_id)
                    
                    if not coupon_batch:
                        logger.warning(f"优惠券批次 {coupon_batch_id} 不存在")
                        failed_items.append({
                            "order_item_id": order_item.id,
                            "product_id": order_item.product_id,
                            "reason": f"优惠券批次 {coupon_batch_id} 不存在"
                        })
                        continue
                    
                    # 检查批次剩余数量是否足够
                    required_quantity = order_item.quantity
                    if coupon_batch.quantity < required_quantity:
                        logger.warning(f"优惠券批次 {coupon_batch_id} 剩余数量不足，需要 {required_quantity} 张，剩余 {coupon_batch.quantity} 张")
                        failed_items.append({
                            "order_item_id": order_item.id,
                            "product_id": order_item.product_id,
                            "coupon_batch_id": coupon_batch_id,
                            "reason": f"批次剩余数量不足，需要{required_quantity}张，剩余{coupon_batch.quantity}张"
                        })
                        continue
                    
                    # 3. 根据订单项数量发放优惠券
                    item_distributed_count = 0
                    for _ in range(required_quantity):
                        try:
                            # 创建优惠券使用记录
                            usage_record_create = CouponUsageRecordCreate(
                                coupon_id=coupon_batch.coupon_id,
                                coupon_batch_id=coupon_batch_id,
                                user_id=order.user_id,
                                order_id=order.id,
                                order_item_id=order_item.id,
                                discount_amount=0.0,  # 初始为0，使用时计算
                                distribution_channel=DistributionChannel.PURCHASE,
                                status=CouponUsageStatus.VALID
                            )
                            
                            usage_record = coupon_usage_record_dao.create(session, usage_record_create)
                            item_distributed_count += 1
                            
                            distributed_coupons.append({
                                "usage_record_id": usage_record.id,
                                "coupon_id": coupon_batch.coupon_id,
                                "coupon_batch_id": coupon_batch_id,
                                "order_id": order.id,
                                "order_item_id": order_item.id,
                                "user_id": order.user_id,
                                "distribution_channel": DistributionChannel.PURCHASE.value
                            })
                            
                        except Exception as e:
                            logger.error(f"为用户 {order.user_id} 发放优惠券失败: {str(e)}")
                            failed_items.append({
                                "order_item_id": order_item.id,
                                "product_id": order_item.product_id,
                                "coupon_batch_id": coupon_batch_id,
                                "reason": f"创建优惠券使用记录失败: {str(e)}"
                            })
                            break
                    
                    # 4. 更新优惠券批次数量
                    if item_distributed_count > 0:
                        new_quantity = coupon_batch.quantity - item_distributed_count
                        coupon_batch_dao.update(session, coupon_batch_id, quantity=new_quantity)
                        total_distributed_count += item_distributed_count
                        
                        logger.info(f"成功为订单 {order.order_no} 的用户 {order.user_id} 发放 {item_distributed_count} 张优惠券，批次ID: {coupon_batch_id}")
                        
                except Exception as e:
                    logger.error(f"处理订单项 {order_item.id} 时出错: {str(e)}")
                    failed_items.append({
                        "order_item_id": order_item.id,
                        "product_id": order_item.product_id,
                        "reason": f"处理订单项失败: {str(e)}"
                    })
                    continue
            
            # 5. 返回发放结果
            return {
                "success": total_distributed_count > 0,
                "message": f"成功发放 {total_distributed_count} 张优惠券" if total_distributed_count > 0 else "没有需要发放的优惠券或发放失败",
                "distributed_count": total_distributed_count,
                "failed_count": len(failed_items),
                "distributed_coupons": distributed_coupons,
                "failed_items": failed_items
            }
            
        except Exception as e:
            logger.error(f"根据订单发放优惠券失败: {str(e)}")
            return {
                "success": False,
                "message": f"发放失败: {str(e)}",
                "distributed_count": 0,
                "failed_count": 0,
                "distributed_coupons": []
            }

    @staticmethod
    def distribute_coupons_by_user_event(session: Session, user_event: UserEvent = None) -> List[Dict[str, Any]]:
        """
        根据用户行为事件发放优惠券
        
        Args:
            session: 数据库会话
            user_event: 用户行为事件对象
            
        Returns:
            List[Dict[str, Any]]: 发放成功的优惠券列表
        """
        if not user_event:
            logger.warning("用户事件为空，无法发放优惠券")
            return []
            
        try:
            logger.info(f"开始处理用户事件: 用户ID {user_event.user_id}, 行为 {user_event.action}")
            
            # 1. 获取所有符合用户行为条件的优惠券发放规则
            applicable_rules = CouponDistributeService._get_applicable_rules_by_behavior(session, user_event)
            if not applicable_rules:
                logger.info(f"没有找到符合用户行为 {user_event.action} 的发放规则")
                return []
                
            logger.info(f"找到 {len(applicable_rules)} 个符合行为条件的发放规则")
            
            # 2. 通过用户特征函数进一步过滤规则
            filtered_rules = CouponDistributeService._filter_rules_by_user_features(session, applicable_rules, user_event)
            if not filtered_rules:
                logger.info("经过用户特征过滤后，没有符合条件的发放规则")
                return []
                
            logger.info(f"经过用户特征过滤后，剩余 {len(filtered_rules)} 个发放规则")
            
            # 3. 循环处理每个规则，进行优惠券发放
            distributed_coupons = []
            for rule in filtered_rules:
                try:
                    rule_coupons = CouponDistributeService._distribute_coupons_for_rule(session, rule, user_event)
                    distributed_coupons.extend(rule_coupons)
                except Exception as e:
                    logger.error(f"处理发放规则 {rule.id} 时出错: {str(e)}")
                    continue
                    
            logger.info(f"总共成功发放 {len(distributed_coupons)} 张优惠券")
            return distributed_coupons
            
        except Exception as e:
            logger.error(f"根据用户事件发放优惠券失败: {str(e)}")
            return []

    @staticmethod
    def _get_applicable_rules_by_behavior(session: Session, user_event: UserEvent) -> List:
        """
        根据用户行为获取符合条件的发放规则
        
        Args:
            session: 数据库会话
            user_event: 用户行为事件
            
        Returns:
            List: 符合条件的发放规则列表
        """
        try:
            # 获取所有活跃的用户行为类型或混合类型的发放规则
            rules = coupon_distribution_rule_dao.get_by_type(session, DistributionRuleType.USER_BEHAVIOR)
            mixed_rules = coupon_distribution_rule_dao.get_by_type(session, DistributionRuleType.USER_FEATURE_AND_BEHAVIOR)
            all_rules = rules + mixed_rules
            
            applicable_rules = []
            
            for rule in all_rules:
                # 检查规则的用户行为函数
                if not rule.user_behavior_func:
                    continue
                    
                # 根据用户事件的action检查是否有对应的行为函数
                action_key = user_event.action.value
                if action_key not in rule.user_behavior_func:
                    continue
                    
                behavior_config = rule.user_behavior_func[action_key]
                func_name = behavior_config.get("func_name")
                params = behavior_config.get("params", {})
                
                if not func_name:
                    continue
                    
                # 获取并执行用户行为函数
                behavior_func = get_user_behavior_function(func_name)
                if not behavior_func:
                    logger.warning(f"未找到用户行为函数: {func_name}")
                    continue
                    
                try:
                    if behavior_func(user_event, params):
                        applicable_rules.append(rule)
                        logger.debug(f"规则 {rule.id} 通过用户行为检查")
                    else:
                        logger.debug(f"规则 {rule.id} 未通过用户行为检查")
                except Exception as e:
                    logger.error(f"执行用户行为函数 {func_name} 时出错: {str(e)}")
                    continue
                    
            return applicable_rules
            
        except Exception as e:
            logger.error(f"获取符合用户行为条件的发放规则失败: {str(e)}")
            return []

    @staticmethod
    def _filter_rules_by_user_features(session: Session, rules: List, user_event: UserEvent) -> List:
        """
        通过用户特征函数过滤发放规则（暂时作为存根函数）
        
        Args:
            session: 数据库会话
            rules: 待过滤的规则列表
            user_event: 用户事件
            
        Returns:
            List: 过滤后的规则列表
        """
        # TODO: 实现用户特征函数检查逻辑
        # 目前暂时返回所有规则，作为存根实现
        logger.debug("用户特征函数检查暂未实现，返回所有规则")
        return rules

    @staticmethod
    def _distribute_coupons_for_rule(session: Session, rule, user_event: UserEvent) -> List[Dict[str, Any]]:
        """
        根据发放规则为用户发放优惠券
        
        Args:
            session: 数据库会话
            rule: 发放规则对象
            user_event: 用户事件
            
        Returns:
            List[Dict[str, Any]]: 发放成功的优惠券列表
        """
        distributed_coupons = []
        
        try:
            # 获取发放内容
            distribution_content = rule.distribution_content
            if not distribution_content:
                logger.warning(f"规则 {rule.id} 没有发放内容")
                return []
                
            for content_item in distribution_content:
                coupon_batch_id = content_item.get("coupon_batch_id")
                quantity = content_item.get("quantity", 1)
                
                if not coupon_batch_id:
                    logger.warning(f"规则 {rule.id} 的发放内容缺少 coupon_batch_id")
                    continue
                    
                # 获取优惠券批次
                coupon_batch = coupon_batch_dao.get(session, coupon_batch_id)
                if not coupon_batch:
                    logger.warning(f"优惠券批次 {coupon_batch_id} 不存在")
                    continue
                    
                # 进行批次约束检查
                allowed_quantity = CouponDistributeService._check_batch_constraints(
                    session, coupon_batch, user_event, quantity
                )
                
                if allowed_quantity <= 0:
                    logger.info(f"用户 {user_event.user_id} 不满足批次 {coupon_batch_id} 的发放约束")
                    continue
                    
                # 发放优惠券
                for _ in range(allowed_quantity):
                    try:
                        # 根据用户行为确定发放渠道
                        distribution_channel = CouponDistributeService._get_distribution_channel_by_action(
                            user_event.action
                        )
                        
                        usage_record_create = CouponUsageRecordCreate(
                            coupon_id=coupon_batch.coupon_id,
                            coupon_batch_id=coupon_batch_id,
                            user_id=user_event.user_id,
                            discount_amount=0.0,  # 初始为0，使用时计算
                            distribution_channel=distribution_channel,
                            status=CouponUsageStatus.VALID
                        )
                        
                        usage_record = coupon_usage_record_dao.create(session, usage_record_create)
                        
                        # 更新批次数量
                        new_quantity = coupon_batch.quantity - 1
                        coupon_batch_dao.update(session, coupon_batch_id, quantity=new_quantity)
                        
                        distributed_coupons.append({
                            "usage_record_id": usage_record.id,
                            "coupon_id": coupon_batch.coupon_id,
                            "coupon_batch_id": coupon_batch_id,
                            "user_id": user_event.user_id,
                            "distribution_channel": distribution_channel.value,
                            "rule_id": rule.id
                        })
                        
                        logger.info(f"成功为用户 {user_event.user_id} 发放优惠券，批次ID: {coupon_batch_id}")
                        
                    except Exception as e:
                        logger.error(f"为用户 {user_event.user_id} 发放优惠券失败: {str(e)}")
                        continue
                        
        except Exception as e:
            logger.error(f"处理发放规则 {rule.id} 时出错: {str(e)}")
            
        return distributed_coupons

    @staticmethod
    def _check_batch_constraints(session: Session, coupon_batch, user_event: UserEvent, requested_quantity: int) -> int:
        """
        检查优惠券批次的发放获取约束
        
        Args:
            session: 数据库会话
            coupon_batch: 优惠券批次对象
            user_event: 用户事件
            requested_quantity: 请求发放数量
            
        Returns:
            int: 允许发放的数量
        """
        try:
            current_time = datetime.now()
            
            # 1. 发放渠道检查
            distribution_channel = CouponDistributeService._get_distribution_channel_by_action(user_event.action)
            if distribution_channel.value not in coupon_batch.distribution_channels:
                logger.debug(f"用户行为 {user_event.action} 对应的发放渠道 {distribution_channel.value} 不在批次允许的发放渠道中")
                return 0
                
            # 2. 获取时间检查
            if coupon_batch.receive_start_time and current_time < coupon_batch.receive_start_time:
                logger.debug(f"当前时间早于批次 {coupon_batch.id} 的获取开始时间")
                return 0
                
            if coupon_batch.receive_end_time and current_time > coupon_batch.receive_end_time:
                logger.debug(f"当前时间晚于批次 {coupon_batch.id} 的获取结束时间")
                return 0
                
            # 3. 发放数量检查
            distribution_limit = CouponDistributeService._check_distribution_quantity_limit(
                session, coupon_batch, requested_quantity
            )
            
            # 4. 用户获取数量检查
            user_limit = CouponDistributeService._check_user_receive_quantity_limit(
                session, coupon_batch, user_event.user_id, requested_quantity
            )
            
            # 返回最小的允许数量
            allowed_quantity = min(distribution_limit, user_limit, requested_quantity)
            
            logger.debug(f"批次 {coupon_batch.id} 约束检查结果: 请求{requested_quantity}, 发放限制{distribution_limit}, 用户限制{user_limit}, 最终允许{allowed_quantity}")
            
            return allowed_quantity
            
        except Exception as e:
            logger.error(f"检查批次约束时出错: {str(e)}")
            return 0

    @staticmethod
    def _check_distribution_quantity_limit(session: Session, coupon_batch, requested_quantity: int) -> int:
        """
        检查发放数量限制
        
        Args:
            session: 数据库会话
            coupon_batch: 优惠券批次对象
            requested_quantity: 请求发放数量
            
        Returns:
            int: 允许发放的数量
        """
        try:
            if not coupon_batch.distribution_quantity or not coupon_batch.distribution_cycle:
                # 如果没有设置发放限制，则不限制
                return requested_quantity
                
            # 计算当前周期的开始时间
            cycle_start = CouponDistributeService._get_cycle_start_time(
                coupon_batch.distribution_cycle, datetime.now()
            )
            
            # 查询当前周期内已发放的数量
            distributed_count = session.query(func.count(CouponUsageRecord.id)).filter(
                CouponUsageRecord.coupon_batch_id == coupon_batch.id,
                CouponUsageRecord.created_at >= cycle_start,
                CouponUsageRecord.status == CouponUsageStatus.VALID
            ).scalar() or 0
            
            # 计算剩余可发放数量
            remaining_quantity = max(0, coupon_batch.distribution_quantity - distributed_count)
            
            return min(remaining_quantity, requested_quantity)
            
        except Exception as e:
            logger.error(f"检查发放数量限制时出错: {str(e)}")
            return 0

    @staticmethod
    def _check_user_receive_quantity_limit(session: Session, coupon_batch, user_id: int, requested_quantity: int) -> int:
        """
        检查用户获取数量限制
        
        Args:
            session: 数据库会话
            coupon_batch: 优惠券批次对象
            user_id: 用户ID
            requested_quantity: 请求发放数量
            
        Returns:
            int: 允许发放的数量
        """
        try:
            if not coupon_batch.receive_quantity or not coupon_batch.receive_cycle:
                # 如果没有设置获取限制，则不限制
                return requested_quantity
                
            # 计算当前周期的开始时间
            cycle_start = CouponDistributeService._get_cycle_start_time(
                coupon_batch.receive_cycle, datetime.now()
            )
            
            # 查询用户在当前周期内已获取的数量
            received_count = session.query(func.count(CouponUsageRecord.id)).filter(
                CouponUsageRecord.coupon_batch_id == coupon_batch.id,
                CouponUsageRecord.user_id == user_id,
                CouponUsageRecord.created_at >= cycle_start,
                CouponUsageRecord.status == CouponUsageStatus.VALID
            ).scalar() or 0
            
            # 计算剩余可获取数量
            remaining_quantity = max(0, coupon_batch.receive_quantity - received_count)
            
            return min(remaining_quantity, requested_quantity)
            
        except Exception as e:
            logger.error(f"检查用户获取数量限制时出错: {str(e)}")
            return 0

    @staticmethod
    def _get_cycle_start_time(cycle: str, current_time: datetime) -> datetime:
        """
        根据周期类型获取周期开始时间
        
        Args:
            cycle: 周期类型（per_day, per_week, per_month等）
            current_time: 当前时间
            
        Returns:
            datetime: 周期开始时间
        """
        if cycle == "per_day":
            return current_time.replace(hour=0, minute=0, second=0, microsecond=0)
        elif cycle == "per_week":
            days_since_monday = current_time.weekday()
            start_of_week = current_time - timedelta(days=days_since_monday)
            return start_of_week.replace(hour=0, minute=0, second=0, microsecond=0)
        elif cycle == "per_month":
            return current_time.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        else:
            # 默认按天计算
            return current_time.replace(hour=0, minute=0, second=0, microsecond=0)

    @staticmethod
    def _get_distribution_channel_by_action(action: UserEventAction) -> DistributionChannel:
        """
        根据用户行为获取对应的发放渠道
        
        Args:
            action: 用户行为
            
        Returns:
            DistributionChannel: 发放渠道
        """
        action_to_channel = {
            UserEventAction.VIEWED: DistributionChannel.VIEW_ACTIVITY,
            UserEventAction.SHARED: DistributionChannel.SHARE_ACTIVITY,
            UserEventAction.PAID: DistributionChannel.PURCHASE,
            UserEventAction.REGISTERED: DistributionChannel.NEW_USER,
            UserEventAction.ORDERED: DistributionChannel.PURCHASE,
        }
        
        return action_to_channel.get(action, DistributionChannel.VIEW_ACTIVITY)

# 创建服务实例
coupon_distribute_service = CouponDistributeService()
