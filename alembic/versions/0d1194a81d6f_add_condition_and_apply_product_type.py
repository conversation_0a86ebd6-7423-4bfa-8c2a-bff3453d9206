"""add_condition_and_apply_product_type

Revision ID: 0d1194a81d6f
Revises: 2a28bd5327b2
Create Date: 2025-09-13 11:52:18.428693

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '0d1194a81d6f'
down_revision: Union[str, None] = '2a28bd5327b2'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('coupons', sa.Column('condition_product_types', sa.JSON(), nullable=True, comment='条件产品类型，存储MealType值列表'))
    op.add_column('coupons', sa.Column('apply_product_types', sa.JSO<PERSON>(), nullable=True, comment='作用产品类型，存储MealType值列表'))
    op.alter_column('products', 'type',
               existing_type=mysql.ENUM('PRODUCT', 'DIRECT_SALE', 'RESERVATION', 'VIRTUAL', collation='utf8mb4_unicode_ci'),
               nullable=True,
               existing_comment='产品类型')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('products', 'type',
               existing_type=mysql.ENUM('PRODUCT', 'DIRECT_SALE', 'RESERVATION', 'VIRTUAL', collation='utf8mb4_unicode_ci'),
               nullable=False,
               existing_comment='产品类型')
    op.drop_column('coupons', 'apply_product_types')
    op.drop_column('coupons', 'condition_product_types')
    # ### end Alembic commands ###
